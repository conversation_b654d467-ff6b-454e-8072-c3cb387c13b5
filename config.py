"""
Configuration management and user input handling for the trading system.
"""

import os
import json
import getpass
from typing import Dict, List, Tuple
from rich.console import Console
from rich.prompt import Prompt, Confirm, IntPrompt, FloatPrompt
from rich.table import Table
from rich.panel import Panel

from services.encryption import EncryptionService
from utils.constants import RISK_PROFILES, STRATEGIES, DEFAULT_MAX_TRADES_PER_MINUTE, DEFAULT_MAX_DOLLAR_PER_TRADE

console = Console()

class ConfigManager:
    """Manages system configuration and user input."""
    
    def __init__(self):
        """Initialize configuration manager."""
        self.config_file = "data/system_config.json"
        self.encrypted_keys_file = "data/encrypted_keys.dat"
        self.encryption_service = EncryptionService()
        
        # Default configuration
        self.config = {
            'max_trades_per_minute': DEFAULT_MAX_TRADES_PER_MINUTE,
            'max_dollar_per_trade': DEFAULT_MAX_DOLLAR_PER_TRADE,
            'risk_profile': 'balanced',
            'strategy': 'scalping',
            'auto_start': True,
            'dashboard_enabled': True,
            'logging_level': 'INFO'
        }
        
        # API keys storage
        self.api_keys = []
        
    def setup_system(self, auto_mode: bool = False) -> bool:
        """Complete system setup process."""
        console.print("\n🔧 [bold cyan]KRAKEN TRADING SYSTEM SETUP[/bold cyan]\n")

        try:
            # Check for .env file first (auto mode)
            if auto_mode or os.path.exists('.env'):
                console.print("🔍 Checking for .env configuration...")
                if self._load_from_env():
                    console.print("✅ Configuration loaded from .env file")

                    # Step 2: Validate API keys
                    if not self._validate_api_keys():
                        console.print("❌ API key validation failed.", style="red")
                        return False

                    # Step 3: Save configuration
                    self._save_config()

                    console.print("\n✅ [bold green]System setup completed successfully![/bold green]\n")
                    return True
                else:
                    console.print("⚠️ .env file found but could not load configuration", style="yellow")
                    if auto_mode:
                        return False

            # Fallback to manual setup
            # Step 1: Load or create configuration
            if not self._load_existing_config():
                console.print("📝 First-time setup detected. Let's configure your system.\n")
                if not self._initial_setup():
                    return False
            else:
                console.print("✅ Existing configuration loaded.\n")
                if not self._verify_existing_setup():
                    return False

            # Step 2: Validate API keys
            if not self._validate_api_keys():
                console.print("❌ API key validation failed.", style="red")
                return False

            # Step 3: Save configuration
            self._save_config()

            console.print("\n✅ [bold green]System setup completed successfully![/bold green]\n")
            return True

        except KeyboardInterrupt:
            console.print("\n❌ Setup cancelled by user.", style="red")
            return False
        except Exception as e:
            console.print(f"\n❌ Setup failed: {e}", style="red")
            return False
    
    def _load_existing_config(self) -> bool:
        """Load existing configuration if available."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                return True
            except Exception as e:
                console.print(f"⚠️ Failed to load config: {e}", style="yellow")
        
        return False
    
    def _initial_setup(self) -> bool:
        """Perform initial system setup."""
        # Welcome message
        welcome_panel = Panel(
            "Welcome to the Kraken Live Trading System!\n\n"
            "This setup will guide you through:\n"
            "• Adding your Kraken API keys\n"
            "• Configuring trading parameters\n"
            "• Setting risk management rules\n\n"
            "⚠️  [bold red]WARNING:[/bold red] This system will trade with real money!\n"
            "Make sure you understand the risks involved.",
            title="Setup Wizard",
            border_style="cyan"
        )
        console.print(welcome_panel)
        
        if not Confirm.ask("\nDo you want to continue with the setup?"):
            return False
        
        # Step 1: API Keys
        console.print("\n📋 [bold]Step 1: API Key Configuration[/bold]")
        if not self._setup_api_keys():
            return False
        
        # Step 2: Trading Parameters
        console.print("\n⚙️ [bold]Step 2: Trading Parameters[/bold]")
        self._setup_trading_parameters()
        
        # Step 3: Risk Management
        console.print("\n🛡️ [bold]Step 3: Risk Management[/bold]")
        self._setup_risk_management()
        
        return True

    def _load_from_env(self) -> bool:
        """Load configuration from .env file."""
        try:
            if not os.path.exists('.env'):
                return False

            env_vars = {}
            with open('.env', 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()

            # Load API keys
            api_keys_loaded = 0
            self.api_keys = []

            for i in range(1, 6):  # Check for 5 API keys
                key_var = f"API_KEY_{i}"
                secret_var = f"API_SECRET_{i}"
                alias_var = f"API_ALIAS_{i}"
                tier_var = f"API_TIER_{i}"

                if (key_var in env_vars and
                    secret_var in env_vars and
                    env_vars[key_var] != f"YOUR_{['FIRST', 'SECOND', 'THIRD', 'FOURTH', 'FIFTH'][i-1]}_KRAKEN_API_KEY_HERE"):

                    api_key_data = {
                        'api_key': env_vars[key_var],
                        'api_secret': env_vars[secret_var],
                        'alias': env_vars.get(alias_var, f"API_Key_{i}"),
                        'tier': env_vars.get(tier_var, 'starter')
                    }

                    self.api_keys.append(api_key_data)
                    api_keys_loaded += 1

            if api_keys_loaded == 0:
                console.print("❌ No valid API keys found in .env file!", style="red")
                return False

            # Load trading configuration
            self.config.update({
                'max_trades_per_minute': int(env_vars.get('MAX_TRADES_PER_MINUTE', 25)),
                'max_dollar_per_trade': float(env_vars.get('MAX_DOLLAR_PER_TRADE', 100)),
                'risk_profile': env_vars.get('RISK_PROFILE', 'balanced'),
                'strategy': env_vars.get('TRADING_STRATEGY', 'scalping'),
                'auto_start': env_vars.get('AUTO_START_TRADING', 'true').lower() == 'true',
                'dashboard_enabled': env_vars.get('DASHBOARD_ENABLED', 'true').lower() == 'true',
                'logging_level': env_vars.get('LOGGING_LEVEL', 'INFO')
            })

            # Get master password and encrypt API keys
            master_password = env_vars.get('MASTER_PASSWORD', 'YourSecureMasterPassword123!')
            if master_password == 'YourSecureMasterPassword123!':
                console.print("⚠️ Using default master password. Change MASTER_PASSWORD in .env for better security.", style="yellow")

            self.encryption_service.set_master_password(master_password)

            # Save encrypted keys
            try:
                self.encryption_service.save_encrypted_keys(self.api_keys, self.encrypted_keys_file)
                console.print(f"✅ Loaded and encrypted {api_keys_loaded} API keys from .env", style="green")
            except Exception as e:
                console.print(f"❌ Failed to encrypt API keys: {e}", style="red")
                return False

            console.print(f"✅ Configuration loaded: {api_keys_loaded} API keys, {self.config['strategy']} strategy, {self.config['risk_profile']} risk", style="green")
            return True

        except Exception as e:
            console.print(f"❌ Error loading .env file: {e}", style="red")
            return False

    def _setup_api_keys(self) -> bool:
        """Setup API keys with encryption."""
        console.print("\nYou need to provide your Kraken API keys for trading.")
        console.print("Keys will be encrypted and stored securely.\n")
        
        # Get master password for encryption
        master_password = getpass.getpass("Enter a master password to encrypt your API keys: ")
        if len(master_password) < 8:
            console.print("❌ Password must be at least 8 characters long.", style="red")
            return False
        
        confirm_password = getpass.getpass("Confirm master password: ")
        if master_password != confirm_password:
            console.print("❌ Passwords do not match.", style="red")
            return False
        
        self.encryption_service.set_master_password(master_password)
        
        # Add API keys
        while True:
            console.print(f"\n📝 Adding API Key #{len(self.api_keys) + 1}")
            
            api_key = Prompt.ask("Enter your Kraken API Key")
            api_secret = getpass.getpass("Enter your Kraken API Secret: ")
            
            # Validate format
            if not self.encryption_service.validate_api_key_format(api_key, api_secret):
                console.print("❌ Invalid API key format. Please check your keys.", style="red")
                continue
            
            alias = Prompt.ask("Enter an alias for this key (optional)", default=f"Key_{len(self.api_keys) + 1}")
            tier = Prompt.ask("Enter API tier", choices=["starter", "intermediate", "pro"], default="starter")
            
            # Store the key
            self.api_keys.append({
                'api_key': api_key,
                'api_secret': api_secret,
                'alias': alias,
                'tier': tier
            })
            
            console.print(f"✅ Added API key: {alias}", style="green")
            
            if len(self.api_keys) >= 5:
                console.print("⚠️ Maximum of 5 API keys recommended for optimal performance.", style="yellow")
            
            if not Confirm.ask("Add another API key?"):
                break
        
        if len(self.api_keys) == 0:
            console.print("❌ At least one API key is required.", style="red")
            return False
        
        # Save encrypted keys
        try:
            self.encryption_service.save_encrypted_keys(self.api_keys, self.encrypted_keys_file)
            console.print(f"✅ Saved {len(self.api_keys)} encrypted API keys.", style="green")
        except Exception as e:
            console.print(f"❌ Failed to save API keys: {e}", style="red")
            return False
        
        return True
    
    def _setup_trading_parameters(self):
        """Setup trading parameters."""
        console.print("\nConfigure your trading parameters:\n")
        
        # Max trades per minute
        self.config['max_trades_per_minute'] = IntPrompt.ask(
            "Maximum trades per minute (global)",
            default=self.config['max_trades_per_minute'],
            show_default=True
        )
        
        # Max dollar amount per trade
        self.config['max_dollar_per_trade'] = FloatPrompt.ask(
            "Maximum dollar amount per trade",
            default=self.config['max_dollar_per_trade'],
            show_default=True
        )
        
        # Strategy selection
        console.print("\nAvailable trading strategies:")
        strategy_table = Table()
        strategy_table.add_column("Strategy", style="cyan")
        strategy_table.add_column("Description", style="white")
        strategy_table.add_column("Timeframe", style="yellow")
        
        for strategy, config in STRATEGIES.items():
            description = f"Min Volume: ${config['min_volume']:,}"
            strategy_table.add_row(strategy.title(), description, config['timeframe'])
        
        console.print(strategy_table)
        
        self.config['strategy'] = Prompt.ask(
            "\nSelect trading strategy",
            choices=list(STRATEGIES.keys()),
            default=self.config['strategy']
        )
    
    def _setup_risk_management(self):
        """Setup risk management parameters."""
        console.print("\nConfigure risk management:\n")
        
        # Risk profile selection
        console.print("Available risk profiles:")
        risk_table = Table()
        risk_table.add_column("Profile", style="cyan")
        risk_table.add_column("Max Position", style="white")
        risk_table.add_column("Stop Loss", style="red")
        risk_table.add_column("Take Profit", style="green")
        
        for profile, config in RISK_PROFILES.items():
            risk_table.add_row(
                profile.title(),
                f"{config['max_position_size']*100:.1f}%",
                f"{config['stop_loss_threshold']*100:.1f}%",
                f"{config['take_profit_threshold']*100:.1f}%"
            )
        
        console.print(risk_table)
        
        self.config['risk_profile'] = Prompt.ask(
            "\nSelect risk profile",
            choices=list(RISK_PROFILES.keys()),
            default=self.config['risk_profile']
        )
        
        # Additional options
        self.config['auto_start'] = Confirm.ask(
            "Auto-start trading after system initialization?",
            default=self.config['auto_start']
        )
        
        self.config['dashboard_enabled'] = Confirm.ask(
            "Enable real-time dashboard?",
            default=self.config['dashboard_enabled']
        )
    
    def _verify_existing_setup(self) -> bool:
        """Verify existing setup and allow modifications."""
        console.print("Current configuration:")
        
        config_table = Table()
        config_table.add_column("Setting", style="cyan")
        config_table.add_column("Value", style="white")
        
        config_table.add_row("Max Trades/Minute", str(self.config['max_trades_per_minute']))
        config_table.add_row("Max $/Trade", f"${self.config['max_dollar_per_trade']}")
        config_table.add_row("Risk Profile", self.config['risk_profile'].title())
        config_table.add_row("Strategy", self.config['strategy'].title())
        config_table.add_row("Auto Start", "Yes" if self.config['auto_start'] else "No")
        config_table.add_row("Dashboard", "Enabled" if self.config['dashboard_enabled'] else "Disabled")
        
        console.print(config_table)
        
        if Confirm.ask("\nDo you want to modify the configuration?"):
            return self._initial_setup()
        
        # Load API keys
        if os.path.exists(self.encrypted_keys_file):
            master_password = getpass.getpass("\nEnter master password to decrypt API keys: ")
            try:
                self.api_keys = self.encryption_service.load_encrypted_keys(
                    self.encrypted_keys_file, master_password
                )
                console.print(f"✅ Loaded {len(self.api_keys)} API keys.", style="green")
            except Exception as e:
                console.print(f"❌ Failed to decrypt API keys: {e}", style="red")
                return False
        else:
            console.print("❌ No encrypted API keys found. Please run setup again.", style="red")
            return False
        
        return True
    
    def _validate_api_keys(self) -> bool:
        """Validate API keys by testing connection."""
        console.print("\n🔍 Validating API keys...")
        
        # This would test each API key
        # For now, just check format
        valid_keys = 0
        for key_data in self.api_keys:
            if self.encryption_service.validate_api_key_format(
                key_data['api_key'], key_data['api_secret']
            ):
                valid_keys += 1
                console.print(f"✅ {key_data['alias']}: Format valid", style="green")
            else:
                console.print(f"❌ {key_data['alias']}: Invalid format", style="red")
        
        if valid_keys == 0:
            console.print("❌ No valid API keys found.", style="red")
            return False
        
        console.print(f"✅ {valid_keys}/{len(self.api_keys)} API keys validated.", style="green")
        return True
    
    def _save_config(self):
        """Save configuration to file."""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def get_config(self) -> Dict:
        """Get current configuration."""
        return self.config.copy()
    
    def get_api_keys(self) -> List[Dict]:
        """Get decrypted API keys."""
        return self.api_keys.copy()
    
    def display_final_summary(self):
        """Display final configuration summary."""
        summary_panel = Panel(
            f"🔧 Configuration Summary:\n\n"
            f"• API Keys: {len(self.api_keys)} configured\n"
            f"• Max Trades/Min: {self.config['max_trades_per_minute']}\n"
            f"• Max $/Trade: ${self.config['max_dollar_per_trade']}\n"
            f"• Risk Profile: {self.config['risk_profile'].title()}\n"
            f"• Strategy: {self.config['strategy'].title()}\n"
            f"• Auto Start: {'Yes' if self.config['auto_start'] else 'No'}\n"
            f"• Dashboard: {'Enabled' if self.config['dashboard_enabled'] else 'Disabled'}\n\n"
            f"⚠️  System ready for live trading with real money!",
            title="System Ready",
            border_style="green"
        )
        console.print(summary_panel)
