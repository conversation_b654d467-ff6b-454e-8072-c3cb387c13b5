2025-06-10 21:53:35,165 - __main__ - INFO - Initializing Kraken Live Trading System...
2025-06-10 21:53:47,135 - __main__ - ERROR - Configuration setup failed
2025-06-10 21:53:47,135 - __main__ - ERROR - System initialization failed
2025-06-10 21:54:48,907 - __main__ - INFO - Initializing Kraken Live Trading System...
2025-06-10 21:56:02,092 - __main__ - INFO - Initializing Kraken Live Trading System...
2025-06-10 22:01:40,023 - services.encryption - INFO - Encrypted API keys saved to data/encrypted_keys.dat
2025-06-10 22:02:32,937 - services.database - INFO - Database initialized successfully
2025-06-10 22:02:32,938 - __main__ - INFO - Database initialized
2025-06-10 22:02:32,939 - agents.key_manager - INFO - Loaded 0 API keys from database
2025-06-10 22:02:33,488 - agents.key_manager - INFO - Added API key: Key_1 (tier: starter)
2025-06-10 22:02:33,970 - agents.key_manager - INFO - Added API key: Key_2 (tier: starter)
2025-06-10 22:02:34,427 - agents.key_manager - INFO - Added API key: Key_3 (tier: starter)
2025-06-10 22:02:34,923 - agents.key_manager - INFO - Added API key: Key_4 (tier: starter)
2025-06-10 22:02:35,390 - agents.key_manager - INFO - Added API key: Key_5 (tier: starter)
2025-06-10 22:02:35,390 - __main__ - INFO - Added 5 API keys to key manager
2025-06-10 22:02:35,390 - __main__ - INFO - Market watcher initialized
2025-06-10 22:02:35,390 - __main__ - INFO - Risk manager initialized
2025-06-10 22:02:35,390 - agents.trader - INFO - Initialized trader trader_1 with key key_0_1749607353
2025-06-10 22:02:35,390 - agents.risk_manager - INFO - Registered trader trader_1 for risk monitoring
2025-06-10 22:02:35,390 - agents.trader - INFO - Initialized trader trader_2 with key key_1_1749607353
2025-06-10 22:02:35,390 - agents.risk_manager - INFO - Registered trader trader_2 for risk monitoring
2025-06-10 22:02:35,390 - agents.trader - INFO - Initialized trader trader_3 with key key_2_1749607354
2025-06-10 22:02:35,390 - agents.risk_manager - INFO - Registered trader trader_3 for risk monitoring
2025-06-10 22:02:35,391 - agents.trader - INFO - Initialized trader trader_4 with key key_3_1749607354
2025-06-10 22:02:35,391 - agents.risk_manager - INFO - Registered trader trader_4 for risk monitoring
2025-06-10 22:02:35,391 - agents.trader - INFO - Initialized trader trader_5 with key key_4_1749607355
2025-06-10 22:02:35,391 - agents.risk_manager - INFO - Registered trader trader_5 for risk monitoring
2025-06-10 22:02:35,391 - __main__ - INFO - Initialized 5 traders
2025-06-10 22:02:35,391 - __main__ - INFO - Dashboard initialized
2025-06-10 22:02:35,391 - __main__ - INFO - System initialization completed successfully
2025-06-10 22:02:35,395 - __main__ - INFO - Starting trading activities...
2025-06-10 22:02:35,395 - agents.market_watcher - INFO - Market monitoring started
2025-06-10 22:02:35,396 - agents.risk_manager - INFO - Risk monitoring started
2025-06-10 22:02:35,396 - agents.trader - INFO - Trader trader_1 started
2025-06-10 22:02:35,396 - agents.trader - INFO - Trader trader_2 started
2025-06-10 22:02:35,396 - agents.trader - INFO - Trader trader_3 started
2025-06-10 22:02:35,396 - agents.trader - INFO - Trader trader_4 started
2025-06-10 22:02:35,396 - agents.trader - INFO - Trader trader_5 started
2025-06-10 22:02:35,398 - __main__ - INFO - All trading activities started
2025-06-10 22:02:35,398 - __main__ - INFO - Starting main trading loop...
2025-06-10 22:02:35,556 - services.kraken_client - ERROR - Kraken API error: EQuery:Unknown asset pair
2025-06-10 22:02:35,556 - services.kraken_client - ERROR - API call failed: Kraken API error: EQuery:Unknown asset pair
2025-06-10 22:02:35,560 - agents.key_manager - ERROR - Request failed for key key_3_1749607354: Kraken API error: EQuery:Unknown asset pair
2025-06-10 22:02:35,560 - agents.market_watcher - ERROR - Failed to get ticker data: Kraken API error: EQuery:Unknown asset pair
2025-06-10 22:16:55,538 - main - INFO - Initializing Kraken Live Trading System...
2025-06-10 22:16:55,613 - services.encryption - INFO - Encrypted API keys saved to data/encrypted_keys.dat
2025-06-10 22:16:55,620 - services.database - INFO - Database initialized successfully
2025-06-10 22:16:55,620 - main - INFO - Database initialized
2025-06-10 22:16:55,621 - agents.key_manager - INFO - Loaded 5 API keys from database
2025-06-10 22:16:56,143 - agents.key_manager - INFO - Added API key: Primary_Trading_Key (tier: starter)
2025-06-10 22:16:56,640 - agents.key_manager - INFO - Added API key: Secondary_Trading_Key (tier: starter)
2025-06-10 22:16:57,111 - agents.key_manager - INFO - Added API key: Backup_Trading_Key (tier: starter)
2025-06-10 22:16:57,600 - agents.key_manager - INFO - Added API key: High_Volume_Key (tier: intermediate)
2025-06-10 22:16:58,056 - agents.key_manager - INFO - Added API key: Premium_Trading_Key (tier: pro)
2025-06-10 22:16:58,056 - main - INFO - Added 5 API keys to key manager
2025-06-10 22:16:58,056 - main - INFO - Market watcher initialized
2025-06-10 22:16:58,056 - main - INFO - Risk manager initialized
2025-06-10 22:16:58,056 - agents.trader - INFO - Initialized trader trader_1 with key key_0_1749608216
2025-06-10 22:16:58,056 - agents.risk_manager - INFO - Registered trader trader_1 for risk monitoring
2025-06-10 22:16:58,057 - agents.trader - INFO - Initialized trader trader_2 with key key_1_1749608216
2025-06-10 22:16:58,057 - agents.risk_manager - INFO - Registered trader trader_2 for risk monitoring
2025-06-10 22:16:58,057 - agents.trader - INFO - Initialized trader trader_3 with key key_2_1749608217
2025-06-10 22:16:58,057 - agents.risk_manager - INFO - Registered trader trader_3 for risk monitoring
2025-06-10 22:16:58,057 - agents.trader - INFO - Initialized trader trader_4 with key key_3_1749608217
2025-06-10 22:16:58,057 - agents.risk_manager - INFO - Registered trader trader_4 for risk monitoring
2025-06-10 22:16:58,057 - agents.trader - INFO - Initialized trader trader_5 with key key_4_1749608218
2025-06-10 22:16:58,057 - agents.risk_manager - INFO - Registered trader trader_5 for risk monitoring
2025-06-10 22:16:58,058 - main - INFO - Initialized 5 traders
2025-06-10 22:16:58,058 - main - INFO - Dashboard initialized
2025-06-10 22:16:58,058 - main - INFO - System initialization completed successfully
2025-06-10 22:16:58,062 - main - INFO - Starting trading activities...
2025-06-10 22:16:58,062 - agents.market_watcher - INFO - Market monitoring started
2025-06-10 22:16:58,063 - agents.risk_manager - INFO - Risk monitoring started
2025-06-10 22:16:58,063 - agents.trader - INFO - Trader trader_1 started
2025-06-10 22:16:58,063 - agents.trader - INFO - Trader trader_2 started
2025-06-10 22:16:58,064 - agents.trader - INFO - Trader trader_3 started
2025-06-10 22:16:58,064 - agents.trader - INFO - Trader trader_4 started
2025-06-10 22:16:58,064 - agents.trader - INFO - Trader trader_5 started
2025-06-10 22:16:58,064 - main - INFO - All trading activities started
2025-06-10 22:16:58,064 - main - INFO - Starting main trading loop...
2025-06-10 22:16:58,200 - services.kraken_client - ERROR - Kraken API error: EQuery:Unknown asset pair
2025-06-10 22:16:58,202 - services.kraken_client - ERROR - API call failed: Kraken API error: EQuery:Unknown asset pair
2025-06-10 22:16:58,207 - agents.key_manager - ERROR - Request failed for key key_4_1749608218: Kraken API error: EQuery:Unknown asset pair
2025-06-10 22:16:58,207 - agents.market_watcher - ERROR - Failed to get ticker data: Kraken API error: EQuery:Unknown asset pair
